import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:core/core.dart';

import '../models/task.dart';

/// Provider for managing task state in Flutter
class TaskProvider extends ChangeNotifier {
  final Core _core;
  final List<TaskModel> _tasks = [];
  bool _isLoading = false;
  String? _error;
  StreamSubscription<String>? _dataChangeSubscription;

  TaskProvider(this._core) {
    _initialize();
  }

  @override
  void dispose() {
    _dataChangeSubscription?.cancel();
    super.dispose();
  }
  
  /// Get the list of tasks
  List<TaskModel> get tasks => List.unmodifiable(_tasks);
  
  /// Get loading state
  bool get isLoading => _isLoading;
  
  /// Get error message
  String? get error => _error;
  
  /// Get node ID
  String get nodeId => _core.nodeId;

  /// Get known nodes count
  int get knownNodesCount => _core.sync.knownNodes.length;

  /// Debug method to print sync status
  void printSyncStatus() => _core.sync.printSyncStatus();
  
  /// Initialize the provider
  Future<void> _initialize() async {
    await _loadTasks();

    // Listen to data changes from EventService
    _dataChangeSubscription = _core.events.dataChangeStream.listen((event) {
      // Reload tasks when new data is received
      _loadTasks();
    });
  }
  
  /// Load tasks from the database
  Future<void> _loadTasks() async {
    _setLoading(true);
    _setError(null);

    try {
      final db = _core.database.database;

      // Get all unique task entity_ids from events (simplified approach)
      final entityResults = await db.rawQuery('''
        SELECT DISTINCT entity_id, MIN(created_at) as created_at
        FROM events
        WHERE type = 'create'
        GROUP BY entity_id
        ORDER BY created_at ASC
      ''');

      _tasks.clear();

      for (final entityRow in entityResults) {
        final entityId = entityRow['entity_id'] as String;
        final createdAt = DateTime.fromMillisecondsSinceEpoch(entityRow['created_at'] as int);

        // Get all records for this entity through events, ordered by most recent first
        final recordResults = await db.rawQuery('''
          SELECT r.field_id, r.value, r.created_at
          FROM records r
          JOIN events e ON r.event_id = e.id
          WHERE e.entity_id = ?
          ORDER BY r.created_at DESC
        ''', [entityId]);

        // Reconstruct task fields from records (most recent value for each field)
        final fields = <String, String?>{};
        final processedFields = <String>{};

        for (final recordRow in recordResults) {
          final fieldId = recordRow['field_id'] as String;

          // Only use the most recent record for each field
          if (!processedFields.contains(fieldId)) {
            fields[fieldId] = recordRow['value'] as String?;
            processedFields.add(fieldId);
          }
        }

        // Create task from fields
        final task = TaskModel.fromFields(entityId, fields, createdAt);

        _tasks.add(task);
      }

      _tasks.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    } catch (e) {
      _setError('Failed to load tasks: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Create a new task
  Future<void> createTask(String title, String description) async {
    _setLoading(true);
    _setError(null);

    try {
      // Use the EventService to create the task
      await _core.events.createTask(
        title: title,
        description: description,
        pushAsync: true,
      );

      // Reload tasks
      await _loadTasks();

    } catch (e) {
      _setError('Failed to create task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Complete a task
  Future<void> completeTask(String taskId) async {
    _setLoading(true);
    _setError(null);

    try {
      // Use the EventService to complete the task
      await _core.events.completeTask(taskId, pushAsync: true);

      // Reload tasks
      await _loadTasks();

    } catch (e) {
      _setError('Failed to complete task: $e');
    } finally {
      _setLoading(false);
    }
  }
  
  /// Refresh tasks and sync (pull from all connected devices)
  Future<void> refresh() async {
    _setLoading(true);
    _setError(null);

    try {
      // Pull from all known nodes
      await _core.sync.pullFromAllNodes();

      // Reload tasks to reflect any new data
      await _loadTasks();

    } catch (e) {
      _setError('Failed to refresh: $e');
    } finally {
      _setLoading(false);
    }
  }
  

  
  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void _setError(String? error) {
    _error = error;
    notifyListeners();
  }

}
