library core;

import 'dart:async';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import 'src/api/api.dart';
import 'src/configuration.dart';
import 'src/database/database_manager.dart';
import 'src/models/node.dart';
import 'src/services/event_service.dart';
import 'src/sync/sync_manager.dart';

export 'src/api/api.dart';
export 'src/configuration.dart';
export 'src/database/database_manager.dart';
export 'src/database/event_store.dart';
export 'src/models/event.dart';
export 'src/models/node.dart';
export 'src/services/event_service.dart';
export 'src/sync/sync_manager.dart';
export 'src/sync/tailscale_client.dart';

/// Main class that orchestrates the system
class Core {
  late final DatabaseManager _databaseManager;
  late final ApiServer _api;
  late final SyncManager _syncManager;
  late final EventService _eventService;
  late final String _nodeId;

  bool _initialized = false;

  /// Get the unique node identifier
  String get nodeId => _nodeId;

  /// Get the database manager instance
  DatabaseManager get database => _databaseManager;

  /// Get the API instance
  ApiServer get api => _api;

  /// Get the sync manager instance
  SyncManager get sync => _syncManager;

  /// Get the event service instance
  EventService get events => _eventService;

  /// Update known nodes in sync manager (called from TailscaleProvider)
  void updateKnownNodes(List<Node> nodes) {
    _syncManager.updateKnownNodes(nodes);
  }

  /// Initialize the core system
  Future<void> initialize({
    String? customNodeId,
    int apiPort = Configuration.defaultApiPort,
    String? tailscaleAccessToken,
  }) async {
    if (_initialized) return;

    // Generate or retrieve node ID
    _nodeId = customNodeId ?? await _getOrCreateNodeId();

    // Initialize database
    _databaseManager = DatabaseManager();
    await _databaseManager.initialize();

    // Initialize API server
    _api = ApiServer(_databaseManager, _nodeId);
    await _api.start(port: apiPort);

    // Initialize sync manager
    _syncManager = SyncManager(
      databaseManager: _databaseManager,
      nodeId: _nodeId,
      apiPort: apiPort,
      tailscaleAccessToken: tailscaleAccessToken,
    );
    await _syncManager.initialize();

    // Initialize event service
    _eventService = EventService(
      databaseManager: _databaseManager,
      syncManager: _syncManager,
      nodeId: _nodeId,
    );

    // Configure API server with event service
    _api.setEventService(_eventService);

    _initialized = true;
  }

  /// Shutdown the core system
  Future<void> shutdown() async {
    if (!_initialized) return;

    await _syncManager.shutdown();
    await _api.stop();
    await _databaseManager.close();
    _eventService.dispose();

    _initialized = false;
  }

  /// Wipe all data and reinitialize system
  Future<void> wipeAllData() async {
    if (!_initialized) return;

    // Shutdown current system
    await _syncManager.shutdown();
    await _api.stop();

    // Wipe database (this will close and recreate it)
    await _databaseManager.wipeAllData();

    // Reinitialize API server with same settings
    _api = ApiServer(_databaseManager, _nodeId);
    await _api.start(port: Configuration.defaultApiPort);

    // Reinitialize sync manager
    _syncManager = SyncManager(
      databaseManager: _databaseManager,
      nodeId: _nodeId,
      apiPort: Configuration.defaultApiPort,
    );
    await _syncManager.initialize();

    // Reinitialize event service
    _eventService = EventService(
      databaseManager: _databaseManager,
      syncManager: _syncManager,
      nodeId: _nodeId,
    );

    // Configure API server with event service
    _api.setEventService(_eventService);

    print('Core system reinitialized after data wipe');
  }

  /// Get or create a persistent node ID
  Future<String> _getOrCreateNodeId() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final nodeIdFile = File(path.join(directory.path, 'node_id.txt'));

      if (await nodeIdFile.exists()) {
        final nodeId = await nodeIdFile.readAsString();
        if (nodeId.isNotEmpty) return nodeId.trim();
      }

      // Generate new node ID
      const uuid = Uuid();
      final newNodeId = uuid.v4();
      await nodeIdFile.writeAsString(newNodeId);

      return newNodeId;
    } catch (e) {
      // Fallback to memory-only UUID if file operations fail
      const uuid = Uuid();
      return uuid.v4();
    }
  }
}
