﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}"
	ProjectSection(ProjectDependencies) = postProject
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4} = {11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{ADBB65C5-16A9-3082-A397-20A045BAEEE1}"
	ProjectSection(ProjectDependencies) = postProject
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF} = {AFA69A13-36FD-39D9-BD3F-EBABC85299DF}
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\\ZERO_CHECK.vcxproj", "{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "drift", "drift.vcxproj", "{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}"
	ProjectSection(ProjectDependencies) = postProject
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A} = {E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4} = {E8DD56AE-491F-3234-A1FB-0E61050FCFD4}
		{613159E7-468D-3F40-9D27-D9D086664E6B} = {613159E7-468D-3F40-9D27-D9D086664E6B}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\flutter\flutter_assemble.vcxproj", "{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}"
	ProjectSection(ProjectDependencies) = postProject
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_app", "..\flutter\flutter_wrapper_app.vcxproj", "{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}"
	ProjectSection(ProjectDependencies) = postProject
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A} = {E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\flutter\flutter_wrapper_plugin.vcxproj", "{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}"
	ProjectSection(ProjectDependencies) = postProject
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A} = {E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "system_tray_plugin", "..\plugins\system_tray\system_tray_plugin.vcxproj", "{613159E7-468D-3F40-9D27-D9D086664E6B}"
	ProjectSection(ProjectDependencies) = postProject
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05} = {EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A} = {E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620} = {2C4DEB73-1FA7-3D81-822C-A9194F8DC620}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}.Debug|x64.ActiveCfg = Debug|x64
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}.Debug|x64.Build.0 = Debug|x64
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}.Profile|x64.ActiveCfg = Profile|x64
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}.Profile|x64.Build.0 = Profile|x64
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}.Release|x64.ActiveCfg = Release|x64
		{AFA69A13-36FD-39D9-BD3F-EBABC85299DF}.Release|x64.Build.0 = Release|x64
		{ADBB65C5-16A9-3082-A397-20A045BAEEE1}.Debug|x64.ActiveCfg = Debug|x64
		{ADBB65C5-16A9-3082-A397-20A045BAEEE1}.Profile|x64.ActiveCfg = Profile|x64
		{ADBB65C5-16A9-3082-A397-20A045BAEEE1}.Release|x64.ActiveCfg = Release|x64
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}.Debug|x64.ActiveCfg = Debug|x64
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}.Debug|x64.Build.0 = Debug|x64
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}.Profile|x64.ActiveCfg = Profile|x64
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}.Profile|x64.Build.0 = Profile|x64
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}.Release|x64.ActiveCfg = Release|x64
		{EA7D3398-E2DD-3FEB-A2E5-3B61191D4F05}.Release|x64.Build.0 = Release|x64
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}.Debug|x64.ActiveCfg = Debug|x64
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}.Debug|x64.Build.0 = Debug|x64
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}.Profile|x64.ActiveCfg = Profile|x64
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}.Profile|x64.Build.0 = Profile|x64
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}.Release|x64.ActiveCfg = Release|x64
		{11CE9EA6-FCBC-31AD-AEC7-34C962B1FEB4}.Release|x64.Build.0 = Release|x64
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}.Debug|x64.ActiveCfg = Debug|x64
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}.Debug|x64.Build.0 = Debug|x64
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}.Profile|x64.ActiveCfg = Profile|x64
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}.Profile|x64.Build.0 = Profile|x64
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}.Release|x64.ActiveCfg = Release|x64
		{E0E412C9-3926-3680-A0E0-EAEDA8E8FB8A}.Release|x64.Build.0 = Release|x64
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}.Debug|x64.ActiveCfg = Debug|x64
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}.Debug|x64.Build.0 = Debug|x64
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}.Profile|x64.ActiveCfg = Profile|x64
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}.Profile|x64.Build.0 = Profile|x64
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}.Release|x64.ActiveCfg = Release|x64
		{E8DD56AE-491F-3234-A1FB-0E61050FCFD4}.Release|x64.Build.0 = Release|x64
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}.Debug|x64.ActiveCfg = Debug|x64
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}.Debug|x64.Build.0 = Debug|x64
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}.Profile|x64.ActiveCfg = Profile|x64
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}.Profile|x64.Build.0 = Profile|x64
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}.Release|x64.ActiveCfg = Release|x64
		{2C4DEB73-1FA7-3D81-822C-A9194F8DC620}.Release|x64.Build.0 = Release|x64
		{613159E7-468D-3F40-9D27-D9D086664E6B}.Debug|x64.ActiveCfg = Debug|x64
		{613159E7-468D-3F40-9D27-D9D086664E6B}.Debug|x64.Build.0 = Debug|x64
		{613159E7-468D-3F40-9D27-D9D086664E6B}.Profile|x64.ActiveCfg = Profile|x64
		{613159E7-468D-3F40-9D27-D9D086664E6B}.Profile|x64.Build.0 = Profile|x64
		{613159E7-468D-3F40-9D27-D9D086664E6B}.Release|x64.ActiveCfg = Release|x64
		{613159E7-468D-3F40-9D27-D9D086664E6B}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C8BBF5BB-F688-321B-A8D8-E6B42441A55C}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
