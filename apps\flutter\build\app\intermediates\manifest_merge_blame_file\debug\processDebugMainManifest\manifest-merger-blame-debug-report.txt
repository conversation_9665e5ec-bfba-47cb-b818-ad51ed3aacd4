1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.itmarck.drift"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:7:5-67
15-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:7:22-64
16    <!-- Permissions for background service -->
17    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
17-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:4:5-77
17-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:4:22-74
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />
18-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:5:5-87
18-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:5:22-84
19    <uses-permission android:name="android.permission.WAKE_LOCK" />
19-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:6:5-68
19-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:6:22-65
20    <!--
21 Required to query activities that can process text, see:
22         https://developer.android.com/training/package-visibility and
23         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
24
25         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
26    -->
27    <queries>
27-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:53:5-58:15
28        <intent>
28-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:54:9-57:18
29            <action android:name="android.intent.action.PROCESS_TEXT" />
29-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:55:13-72
29-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:55:21-70
30
31            <data android:mimeType="text/plain" />
31-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:56:13-50
31-->D:\code\drift\apps\flutter\android\app\src\main\AndroidManifest.xml:56:19-48
32        </intent>
33    </queries>
34
35    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
35-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-81
35-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-78
36
37    <permission
37-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
38        android:name="com.itmarck.drift.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
39        android:protectionLevel="signature" />
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
40
41    <uses-permission android:name="com.itmarck.drift.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
42
43    <application
44        android:name="android.app.Application"
45        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
46        android:debuggable="true"
47        android:extractNativeLibs="true"
48        android:icon="@mipmap/ic_launcher"
49        android:label="Drift" >
50        <activity
51            android:name="com.itmarck.drift.MainActivity"
52            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
53            android:exported="true"
54            android:hardwareAccelerated="true"
55            android:launchMode="singleTop"
56            android:taskAffinity=""
57            android:theme="@style/LaunchTheme"
58            android:windowSoftInputMode="adjustResize" >
59
60            <!--
61                 Specifies an Android theme to apply to this Activity as soon as
62                 the Android process has started. This theme is visible to the user
63                 while the Flutter UI initializes. After that, this theme continues
64                 to determine the Window background behind the Flutter UI.
65            -->
66            <meta-data
67                android:name="io.flutter.embedding.android.NormalTheme"
68                android:resource="@style/NormalTheme" />
69
70            <intent-filter>
71                <action android:name="android.intent.action.MAIN" />
72
73                <category android:name="android.intent.category.LAUNCHER" />
74            </intent-filter>
75        </activity>
76        <!--
77             Don't delete the meta-data below.
78             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
79        -->
80        <meta-data
81            android:name="flutterEmbedding"
82            android:value="2" />
83
84        <!-- Background service -->
85        <service
86            android:name="id.flutter.flutter_background_service.BackgroundService"
87            android:enabled="true"
87-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-35
88            android:exported="false"
89            android:foregroundServiceType="dataSync"
90            android:stopWithTask="false" />
90-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-41
91
92        <receiver
92-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:9-21:39
93            android:name="id.flutter.flutter_background_service.WatchdogReceiver"
93-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-82
94            android:enabled="true"
94-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-35
95            android:exported="true" />
95-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
96        <receiver
96-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:9-31:20
97            android:name="id.flutter.flutter_background_service.BootReceiver"
97-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-78
98            android:enabled="true"
98-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-35
99            android:exported="true" >
99-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-36
100            <intent-filter>
100-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-30:29
101                <action android:name="android.intent.action.BOOT_COMPLETED" />
101-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:17-79
101-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:25-76
102                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
102-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:17-82
102-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:25-79
103                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
103-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-84
103-->[:flutter_background_service_android] D:\code\drift\apps\flutter\build\flutter_background_service_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:25-81
104            </intent-filter>
105        </receiver>
106
107        <uses-library
107-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
108            android:name="androidx.window.extensions"
108-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
109            android:required="false" />
109-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
110        <uses-library
110-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
111            android:name="androidx.window.sidecar"
111-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
112            android:required="false" />
112-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
113
114        <provider
114-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
115            android:name="androidx.startup.InitializationProvider"
115-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
116            android:authorities="com.itmarck.drift.androidx-startup"
116-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
117            android:exported="false" >
117-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
118            <meta-data
118-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
119-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
120                android:value="androidx.startup" />
120-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
122-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
123                android:value="androidx.startup" />
123-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
124        </provider>
125
126        <receiver
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
127            android:name="androidx.profileinstaller.ProfileInstallReceiver"
127-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
128            android:directBootAware="false"
128-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
129            android:enabled="true"
129-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
130            android:exported="true"
130-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
131            android:permission="android.permission.DUMP" >
131-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
132            <intent-filter>
132-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
133                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
133-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
134            </intent-filter>
135            <intent-filter>
135-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
136                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
137            </intent-filter>
138            <intent-filter>
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
139                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
139-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
140            </intent-filter>
141            <intent-filter>
141-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
142                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
142-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
143            </intent-filter>
144        </receiver>
145    </application>
146
147</manifest>
