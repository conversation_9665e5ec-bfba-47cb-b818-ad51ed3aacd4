# Background Service - API en Segundo Plano

## Implementación Completada

La API de Drift ahora puede seguir funcionando cuando se cierra la aplicación:

- **Android**: Servicio en primer plano con notificación
- **Desktop**: Icono en bandeja del sistema

## Para Activar

1. Instalar dependencias:
```bash
cd apps/flutter
flutter pub get
```

2. Probar:
```bash
flutter run
```

## Archivos Modificados

- `pubspec.yaml` - 2 dependencias agregadas
- `main.dart` - Inicialización del servicio
- `background_service.dart` - Lógica multiplataforma (55 líneas)
- `AndroidManifest.xml` - Permisos para Android

## Comportamiento

**Android**: Notificación "Drift Sync Service" mantiene API activa
**Desktop**: Minimiza a bandeja del sistema, API sigue funcionando
