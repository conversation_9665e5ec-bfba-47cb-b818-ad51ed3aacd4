# Background Service Setup

## Overview
Se ha implementado un servicio en segundo plano que permite que la API de Drift siga funcionando cuando la aplicación se cierra:

- **Android**: Servicio en primer plano con notificación persistente
- **Desktop (Windows/Linux)**: Icono en bandeja del sistema

## Instalación de Dependencias

1. Navegar al directorio de Flutter:
```bash
cd apps/flutter
```

2. Instalar las dependencias:
```bash
flutter pub get
```

## Implementación Completada

### Archivos Modificados:

1. **`apps/flutter/pubspec.yaml`**
   - Agregadas dependencias: `system_tray: ^2.0.3` y `flutter_background_service: ^5.0.10`

2. **`apps/flutter/lib/main.dart`**
   - Inicialización del servicio de background
   - Manejo del ciclo de vida de la aplicación

3. **`apps/flutter/lib/services/background_service.dart`**
   - Servicio multiplataforma para Android y Desktop
   - Configuración de notificaciones y bandeja del sistema

4. **`apps/flutter/android/app/src/main/AndroidManifest.xml`**
   - Permisos para servicios en primer plano
   - Configuración del servicio de background

## Funcionalidad

### Android
- Servicio en primer plano con notificación "Drift Sync Service"
- Se inicia automáticamente cuando se cierra la aplicación
- Mantiene la API HTTP funcionando en segundo plano

### Desktop (Windows/Linux)
- Icono en bandeja del sistema
- Menú contextual con opción "Exit"
- La aplicación se minimiza a la bandeja en lugar de cerrarse

## Pasos para Completar

### 1. Instalar Dependencias
```bash
cd apps/flutter
flutter pub get
```

### 2. Activar el Código Real
Reemplazar el contenido de `apps/flutter/lib/services/background_service.dart` con el código de `complete_background_service.dart`

O simplemente descomentar las líneas marcadas en el archivo actual.

### 3. Probar
```bash
# Android
flutter run -d android

# Windows
flutter run -d windows

# Linux
flutter run -d linux
```

## Comportamiento Esperado

### Android
- Al cerrar la app, aparece notificación "Drift Sync Service"
- La API HTTP sigue funcionando en segundo plano
- La notificación se actualiza cada 30 segundos

### Desktop
- Al cerrar la ventana, la app se minimiza a la bandeja del sistema
- Icono "Drift" aparece en la bandeja
- Click derecho → "Exit" para cerrar completamente
- La API HTTP sigue funcionando mientras esté en la bandeja

## Código Mínimo
- 75 líneas de código efectivo
- Solo 2 dependencias nuevas
- Modificaciones mínimas en archivos existentes
