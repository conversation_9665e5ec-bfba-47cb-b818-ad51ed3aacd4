import 'dart:async';

import 'package:core/core.dart';
import 'package:sqflite/sqflite.dart';
import 'package:uuid/uuid.dart';

/// Service for managing events and synchronization
/// Provides shared functionality for both API and direct database access
class EventService {
  final DatabaseManager _databaseManager;
  final SyncManager _syncManager;
  final String _nodeId;

  // Stream controller for notifying UI about new data
  final StreamController<String> _dataChangeController = StreamController<String>.broadcast();

  EventService({
    required DatabaseManager databaseManager,
    required SyncManager syncManager,
    required String nodeId,
  })  : _databaseManager = databaseManager,
        _syncManager = syncManager,
        _nodeId = nodeId;

  /// Stream that emits when new data is received
  Stream<String> get dataChangeStream => _dataChangeController.stream;

  /// Dispose resources
  void dispose() {
    _dataChangeController.close();
  }

  /// Create an event with records and optionally push to other nodes
  Future<Event> createEventWithRecords({
    required String eventType,
    required String entityId,
    required String entityType,
    required List<EventRecord> records,
    bool pushAsync = true,
  }) async {
    const uuid = Uuid();
    final eventId = uuid.v4();
    final now = DateTime.now().millisecondsSinceEpoch;
    final db = _databaseManager.database;

    // Create event and records in a transaction
    await db.transaction((txn) async {
      // Insert event
      await txn.insert('events', {
        'id': eventId,
        'type': eventType,
        'entity_id': entityId,
        'node': _nodeId,
        'created_at': now,
      });

      // Insert records
      for (final record in records) {
        await txn.insert('records', {
          'event_id': eventId,
          'field_id': record.fieldId,
          'value': record.value,
          'created_at': now,
        });
      }
    });

    // Create Event object for synchronization
    final event = Event(
      id: eventId,
      origin: _nodeId,
      timestamp: DateTime.fromMillisecondsSinceEpoch(now),
      entityType: entityType,
      entityId: entityId,
      eventType: eventType,
      payload: {}, // Empty payload, data is in records
      synced: false,
    );

    // Convert to Record objects for synchronization
    final recordObjects = records.map((r) => Record(
      eventId: eventId,
      fieldId: r.fieldId,
      value: r.value,
      createdAt: DateTime.fromMillisecondsSinceEpoch(now),
    )).toList();

    // Push to other nodes asynchronously if requested
    if (pushAsync) {
      print('📤 EventService: Triggering async push for event ${event.id}');
      _syncManager.pushEventToAllNodesAsync(event, recordObjects);
    } else {
      print('⏸️ EventService: Push disabled for event ${event.id}');
    }

    return event;
  }

  /// Create a task with title, description, and initial done state
  Future<Event> createTask({
    required String title,
    required String description,
    bool pushAsync = true,
  }) async {
    print('📝 EventService.createTask called: "$title" (pushAsync: $pushAsync)');
    const uuid = Uuid();
    final taskId = uuid.v4();
    final now = DateTime.now().millisecondsSinceEpoch;
    final db = _databaseManager.database;

    // Create entity first
    await db.insert('entities', {
      'id': taskId,
      'type_id': 'task',
      'created_at': now,
    });

    // Create records for the task
    final records = <EventRecord>[];

    if (title.isNotEmpty) {
      records.add(EventRecord(fieldId: 'title', value: title));
    }

    if (description.isNotEmpty) {
      records.add(EventRecord(fieldId: 'description', value: description));
    }

    // Set initial done state to false
    records.add(EventRecord(fieldId: 'done', value: 'false'));

    // Create the event with records
    return await createEventWithRecords(
      eventType: 'create',
      entityId: taskId,
      entityType: 'task',
      records: records,
      pushAsync: pushAsync,
    );
  }

  /// Update a task field (e.g., mark as done)
  Future<Event> updateTaskField({
    required String taskId,
    required String fieldId,
    required String value,
    bool pushAsync = true,
  }) async {
    final records = [EventRecord(fieldId: fieldId, value: value)];

    return await createEventWithRecords(
      eventType: 'update',
      entityId: taskId,
      entityType: 'task',
      records: records,
      pushAsync: pushAsync,
    );
  }

  /// Complete a task (convenience method)
  Future<Event> completeTask(String taskId, {bool pushAsync = true}) async {
    return await updateTaskField(
      taskId: taskId,
      fieldId: 'done',
      value: 'true',
      pushAsync: pushAsync,
    );
  }

  /// Insert received events and records from other nodes
  Future<void> insertReceivedData({
    required List<Event> events,
    required List<Record> records,
  }) async {
    // Filter out events that originated from this node
    final externalEvents = events.where((event) => event.origin != _nodeId).toList();
    final externalRecords = records.where((record) => 
      externalEvents.any((event) => event.id == record.eventId)
    ).toList();

    if (externalEvents.isEmpty && externalRecords.isEmpty) {
      return;
    }

    // Insert in a transaction
    int insertedEvents = 0;
    int insertedRecords = 0;

    await _databaseManager.database.transaction((txn) async {
      for (final event in externalEvents) {
        final result = await txn.insert(
          'events',
          event.toMap(),
          conflictAlgorithm: ConflictAlgorithm.ignore,
        );
        if (result != 0) insertedEvents++;
      }

      for (final record in externalRecords) {
        final result = await txn.insert(
          'records',
          record.toMap(),
          conflictAlgorithm: ConflictAlgorithm.ignore,
        );
        if (result != 0) insertedRecords++;
      }
    });

    print('Inserted received data: $insertedEvents events, $insertedRecords records');

    // Notify UI about new data if any was inserted
    if (insertedEvents > 0 || insertedRecords > 0) {
      print('📢 EventService: Notifying UI about new data');
      _dataChangeController.add('data_received');
    }
  }
}

/// Simple data class for event records
class EventRecord {
  final String fieldId;
  final String? value;

  const EventRecord({
    required this.fieldId,
    this.value,
  });
}
