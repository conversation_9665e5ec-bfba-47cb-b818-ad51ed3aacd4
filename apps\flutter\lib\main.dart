import 'dart:io';
import 'package:core/core.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:window_manager/window_manager.dart';

import 'providers/task_provider.dart';
import 'providers/tailscale_provider.dart';
import 'screens/task_list_screen.dart';
import 'services/background_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize window manager for desktop
  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    await windowManager.ensureInitialized();
    await windowManager.setPreventClose(true);
  }

  // Initialize Drift core
  final driftCore = Core();
  await driftCore.initialize(
    // You can configure Tailscale here if needed
    // tailscaleAccessToken: 'your-token-here',
  );

  // Initialize background service
  await BackgroundService.initialize(driftCore);

  runApp(DriftApp(driftCore: driftCore));
}

class DriftApp extends StatefulWidget {
  final Core driftCore;

  const DriftApp({super.key, required this.driftCore});

  @override
  State<DriftApp> createState() => _DriftAppState();
}

class _DriftAppState extends State<DriftApp>
    with WidgetsBindingObserver, WindowListener {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      windowManager.addListener(this);
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      windowManager.removeListener(this);
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (Platform.isAndroid) {
      if (state == AppLifecycleState.paused ||
          state == AppLifecycleState.detached ||
          state == AppLifecycleState.hidden) {
        // App is being minimized/closed, ensure background service is running
        BackgroundService.startService();
      }
    } else {
      if (state == AppLifecycleState.detached) {
        // Desktop: App is being closed, start background service
        BackgroundService.startService();
      } else if (state == AppLifecycleState.hidden) {
        // Desktop: Window is hidden (minimized to tray)
        BackgroundService.startService();
      }
    }
  }

  @override
  void onWindowClose() async {
    // Hide window instead of closing on desktop
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await windowManager.hide();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => TaskProvider(widget.driftCore),
        ),
        ChangeNotifierProvider(
          create: (context) => TailscaleProvider(widget.driftCore),
        ),
      ],
      child: MaterialApp(
        title: 'Drift - Distributed Tasks',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const TaskListScreen(),
      ),
    );
  }
}
