import 'dart:async';
import 'dart:io';
import 'package:core/core.dart';
import 'package:system_tray/system_tray.dart';
import 'package:flutter_background_service/flutter_background_service.dart';

class BackgroundService {
  static SystemTray? _systemTray;

  static Future<void> initialize(Core driftCore) async {
    if (Platform.isAndroid) {
      await FlutterBackgroundService().configure(
        iosConfiguration: IosConfiguration(),
        androidConfiguration: AndroidConfiguration(
          onStart: onStart,
          autoStart: false,
          isForegroundMode: true,
          notificationChannelId: 'drift_sync',
          initialNotificationTitle: 'Drift Sync Service',
          initialNotificationContent: 'Sync service running',
        ),
      );
    } else if (Platform.isWindows || Platform.isLinux) {
      _systemTray = SystemTray();
      await _systemTray!.initSystemTray(
        title: 'Drift',
        iconPath: 'lib/assets/logo.png',
      );
      await _systemTray!.setContextMenu([
        MenuItem(label: 'Exit', onClicked: () => exit(0)),
      ]);
    }
  }

  @pragma('vm:entry-point')
  static void onStart(ServiceInstance service) {
    service.on('stopService').listen((_) => service.stopSelf());
    Timer.periodic(const Duration(seconds: 30), (_) {
      if (service is AndroidServiceInstance) {
        service.setForegroundNotificationInfo(
          title: "Drift Sync Service",
          content: "Sync service running",
        );
      }
    });
  }

  static Future<void> startService() async {
    if (Platform.isAndroid) {
      final service = FlutterBackgroundService();
      if (!(await service.isRunning())) {
        service.startService();
      }
    }
  }

  static void dispose() {
    // SystemTray 0.1.1 doesn't have destroy method
    _systemTray = null;
  }
}
