import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:core/core.dart';
import 'package:system_tray/system_tray.dart';
import 'package:flutter_background_service/flutter_background_service.dart';

const icon = 'lib/assets/logo.ico';

@pragma('vm:entry-point')
void onStart(ServiceInstance service) {
  service.on('stopService').listen((_) => service.stopSelf());
  Timer.periodic(const Duration(seconds: 30), (_) {
    if (service is AndroidServiceInstance) {
      service.setForegroundNotificationInfo(
        title: "Drift Sync Service",
        content: "Sync service running",
      );
    }
  });
}

class BackgroundService {
  static SystemTray? _systemTray;
  static bool _initialized = false;

  static Future<void> initialize(Core driftCore) async {
    if (_initialized) return;

    if (Platform.isAndroid) {
      try {
        await FlutterBackgroundService().configure(
          iosConfiguration: IosConfiguration(),
          androidConfiguration: AndroidConfiguration(
            onStart: onStart,
            autoStart: false,
            isForegroundMode: true,
            notificationChannelId: 'drift_sync',
            initialNotificationTitle: 'Drift Sync Service',
            initialNotificationContent: 'Sync service running',
          ),
        );
      } catch (e) {
        if (kDebugMode) print('Android background service failed: $e');
      }
    } else if (Platform.isWindows || Platform.isLinux) {
      try {
        _systemTray = SystemTray();
        await _systemTray!.initSystemTray(title: 'Drift', iconPath: icon);
        await _systemTray!.setContextMenu([
          MenuItem(label: 'Show Drift', onClicked: () {}),
          MenuItem(label: 'Exit', onClicked: () => exit(0)),
        ]);
        _systemTray!.registerSystemTrayEventHandler((eventName) {
          if (eventName == 'leftMouseUp') {
            _systemTray!.popUpContextMenu();
          }
        });
      } catch (e) {
        if (kDebugMode) print('System tray failed: $e');
        _systemTray = null;
      }
    }

    _initialized = true;
  }

  static Future<void> startService() async {
    if (Platform.isAndroid) {
      final service = FlutterBackgroundService();
      if (!(await service.isRunning())) {
        service.startService();
      }
    }
  }

  static void dispose() {
    _systemTray = null;
    _initialized = false;
  }
}
