import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:core/core.dart';
import 'package:system_tray/system_tray.dart';
import 'package:window_manager/window_manager.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

const icon = 'lib/assets/logo.ico';

// Android background service temporarily disabled
// @pragma('vm:entry-point')
// void onStart(ServiceInstance service) { ... }

class BackgroundService {
  static SystemTray? _systemTray;
  static FlutterLocalNotificationsPlugin? _notifications;
  static bool _initialized = false;

  static Future<void> initialize(Core driftCore) async {
    if (_initialized) return;

    if (Platform.isAndroid) {
      await _initializeAndroidNotifications();
    } else if (Platform.isWindows || Platform.isLinux) {
      try {
        _systemTray = SystemTray();
        await _systemTray!.initSystemTray(title: 'Drift', iconPath: icon);
        await _systemTray!.setContextMenu([
          MenuItem(label: 'Show Drift', onClicked: () => _showWindow()),
          MenuItem(label: 'Exit', onClicked: () => exit(0)),
        ]);
        _systemTray!.registerSystemTrayEventHandler((eventName) {
          if (eventName == 'rightMouseUp') {
            _systemTray!.popUpContextMenu();
          } else if (eventName == 'leftMouseUp') {
            _showWindow();
          }
        });
      } catch (e) {
        if (kDebugMode) print('System tray failed: $e');
        _systemTray = null;
      }
    }

    _initialized = true;
  }

  static Future<void> _initializeAndroidNotifications() async {
    _notifications = FlutterLocalNotificationsPlugin();

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const initSettings = InitializationSettings(android: androidSettings);

    await _notifications!.initialize(initSettings);

    // Show persistent notification
    await _showPersistentNotification();
  }

  static Future<void> _showPersistentNotification() async {
    const androidDetails = AndroidNotificationDetails(
      'drift_server',
      'Drift Server',
      channelDescription: 'Local server running in background',
      importance: Importance.low,
      priority: Priority.low,
      ongoing: true,
      autoCancel: false,
      showWhen: false,
    );

    const notificationDetails = NotificationDetails(android: androidDetails);

    await _notifications!.show(
      1,
      'Drift Server',
      'Local server running on port 4547',
      notificationDetails,
    );
  }

  static Future<void> startService() async {
    if (Platform.isAndroid) {
      await _showPersistentNotification();
    }
  }

  static void dispose() {
    _systemTray = null;
    _initialized = false;
  }

  static void _showWindow() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await windowManager.show();
      await windowManager.focus();
    }
  }
}
