import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:core/core.dart';
import 'package:system_tray/system_tray.dart';
import 'package:window_manager/window_manager.dart';
import 'package:flutter_background_service/flutter_background_service.dart';

const icon = 'lib/assets/logo.ico';

// HTTP Server Isolate Entry Point
@pragma('vm:entry-point')
void serverIsolate(SendPort sendPort) async {
  final core = Core();
  await core.initialize();
  sendPort.send('Server started in isolate');

  // Keep isolate alive
  Timer.periodic(const Duration(minutes: 1), (_) {
    sendPort.send('Server running');
  });
}

// Background Service Entry Point
@pragma('vm:entry-point')
void onStart(ServiceInstance service) async {
  // Start HTTP server in isolate
  final receivePort = ReceivePort();
  await Isolate.spawn(serverIsolate, receivePort.sendPort);

  receivePort.listen((message) {
    if (service is AndroidServiceInstance) {
      service.setForegroundNotificationInfo(
        title: "Drift Server",
        content: message.toString(),
      );
    }
  });

  service.on('stopService').listen((_) => service.stopSelf());
}

class BackgroundService {
  static SystemTray? _systemTray;
  static bool _initialized = false;

  static Future<void> initialize(Core driftCore) async {
    if (_initialized) return;

    if (Platform.isAndroid) {
      await _configureAndroidService();
    } else if (Platform.isWindows || Platform.isLinux) {
      try {
        _systemTray = SystemTray();
        await _systemTray!.initSystemTray(title: 'Drift', iconPath: icon);
        await _systemTray!.setContextMenu([
          MenuItem(label: 'Show Drift', onClicked: () => _showWindow()),
          MenuItem(label: 'Exit', onClicked: () => exit(0)),
        ]);
        _systemTray!.registerSystemTrayEventHandler((eventName) {
          if (eventName == 'rightMouseUp') {
            _systemTray!.popUpContextMenu();
          } else if (eventName == 'leftMouseUp') {
            _showWindow();
          }
        });
      } catch (e) {
        if (kDebugMode) print('System tray failed: $e');
        _systemTray = null;
      }
    }

    _initialized = true;
  }

  static Future<void> _configureAndroidService() async {
    await FlutterBackgroundService().configure(
      iosConfiguration: IosConfiguration(),
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        autoStart: false,
        isForegroundMode: true,
        notificationChannelId: 'drift_server',
        initialNotificationTitle: 'Drift Server',
        initialNotificationContent: 'Starting server...',
      ),
    );
  }

  static Future<void> startService() async {
    if (Platform.isAndroid) {
      try {
        final service = FlutterBackgroundService();
        if (!(await service.isRunning())) {
          service.startService();
        }
      } catch (e) {
        if (kDebugMode) print('Failed to start service: $e');
      }
    }
  }

  static void dispose() {
    _systemTray = null;
    _initialized = false;
  }

  static void _showWindow() async {
    if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
      await windowManager.show();
      await windowManager.focus();
    }
  }
}
