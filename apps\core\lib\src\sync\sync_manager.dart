import 'dart:async';
import 'dart:convert';

import 'package:core/core.dart';
import 'package:http/http.dart' as http;
import 'package:sqflite/sqflite.dart';

/// Result of pulling data from a single node
class _PullResult {
  final List<Event> events;
  final List<Record> records;

  _PullResult({required this.events, required this.records});
}

/// Manages synchronization between nodes
class SyncManager {
  final DatabaseManager _databaseManager;
  final String _nodeId;
  final TailscaleClient? _tailscaleClient;

  List<Node> _knownNodes = [];
  bool _isInitialized = false;

  SyncManager({
    required DatabaseManager databaseManager,
    required String nodeId,
    required int apiPort,
    String? tailscaleAccessToken,
    String? tailnet,
  })  : _databaseManager = databaseManager,
        _nodeId = nodeId,
        _tailscaleClient = tailscaleAccessToken != null
            ? TailscaleClient(
                accessToken: tailscaleAccessToken,
                tailnet: tailnet ??
                    TailscaleClient.extractTailnet(
                        tailscaleAccessToken, tailnet),
              )
            : null;

  /// Initialize the sync manager
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Test Tailscale connection if configured
    if (_tailscaleClient != null) {
      final connected = await _tailscaleClient!.testConnection();
      if (connected) {
        print('Tailscale API connection successful');
        await _discoverNodes();
      } else {
        print('Warning: Tailscale API connection failed');
      }
    } else {
      print('Tailscale not configured, running in local mode');
    }

    // Start periodic sync
    _startPeriodicSync();

    _isInitialized = true;
  }

  /// Shutdown the sync manager
  Future<void> shutdown() async {
    _isInitialized = false;
  }

  /// Start periodic synchronization (disabled for manual control)
  void _startPeriodicSync() {
    // Periodic sync disabled - only manual sync via refresh button
    print('Periodic sync disabled - using manual sync only');
  }

  /// Discover nodes using Tailscale API
  Future<void> _discoverNodes() async {
    if (_tailscaleClient == null) return;

    try {
      final nodes = await _tailscaleClient!.getDevices();

      // Filter out this node
      _knownNodes = nodes.where((node) => node.id != _nodeId).toList();

      print(
          'Discovered ${_knownNodes.length} nodes: ${_knownNodes.map((n) => n.name).join(', ')}');
    } catch (e) {
      print('Failed to discover nodes: $e');
    }
  }

  /// Perform synchronization with all known nodes (disabled - manual only)
  Future<void> _performSync() async {
    // Disabled - only manual sync via refresh button
    print('Automatic sync disabled');
  }

  // Removed _sendEventsToNode - using manual sync only

  // Removed complex method - using simple _pullFromSingleNode instead

  /// Manually trigger synchronization
  Future<void> syncNow() async {
    await _performSync();
  }

  /// Pull from all connected nodes in parallel (for refresh button)
  Future<void> pullFromAllNodes() async {
    if (_knownNodes.isEmpty) {
      print('No known nodes to pull from');
      return;
    }

    print('Starting parallel pull from ${_knownNodes.length} nodes...');

    // Create futures for all nodes with timeout
    final futures = _knownNodes.map((node) =>
      _pullFromSingleNodeWithTimeout(node)
    ).toList();

    // Wait for all futures to complete (or timeout)
    final results = await Future.wait(futures, eagerError: false);

    // Collect all successful results
    final allEvents = <Event>[];
    final allRecords = <Record>[];
    int successfulNodes = 0;

    for (int i = 0; i < results.length; i++) {
      final result = results[i];
      final node = _knownNodes[i];

      if (result != null) {
        allEvents.addAll(result.events);
        allRecords.addAll(result.records);
        successfulNodes++;
        print('Successfully pulled from ${node.name}: ${result.events.length} events, ${result.records.length} records');
      } else {
        print('Failed to pull from ${node.name} (timeout or error)');
      }
    }

    // Insert all data in a single transaction if we have any
    if (allEvents.isNotEmpty || allRecords.isNotEmpty) {
      await _insertPulledDataInTransaction(allEvents, allRecords);
    }

    print('Finished parallel pull: $successfulNodes/${_knownNodes.length} nodes successful, ${allEvents.length} total events, ${allRecords.length} total records');
  }

  /// Push a specific event with its records to all nodes (synchronous, waits for all)
  Future<void> pushEventToAllNodes(Event event, List<Record> records) async {
    if (_knownNodes.isEmpty) {
      print('No known nodes to push to');
      return;
    }

    print('Pushing event ${event.id} to ${_knownNodes.length} nodes...');

    for (final node in _knownNodes) {
      try {
        final url = '${node.baseUrl}/push';

        final response = await http
            .post(
              Uri.parse(url),
              headers: {'Content-Type': 'application/json'},
              body: jsonEncode({
                'events': [event.toJson()],
                'records': records.map((r) => r.toJson()).toList(),
                'sender_node_id': _nodeId,
              }),
            )
            .timeout(const Duration(seconds: 10));

        if (response.statusCode == 200) {
          print('Successfully pushed event to ${node.name}');
        } else {
          print('Failed to push event to ${node.name}: ${response.statusCode}');
        }
      } catch (e) {
        print('Failed to push event to node ${node.name}: $e');
        // Continue with other nodes even if one fails
      }
    }

    print('Finished pushing event to all nodes');
  }

  /// Push a specific event with its records to all nodes asynchronously (fire-and-forget)
  void pushEventToAllNodesAsync(Event event, List<Record> records) {
    print('🚀 pushEventToAllNodesAsync called for event ${event.id}');
    print('🔍 Known nodes count: ${_knownNodes.length}');

    if (_knownNodes.isEmpty) {
      print('❌ No known nodes to push to (async) - event ${event.id} will not be synchronized');
      print('💡 Make sure devices are connected to Tailscale and nodes are discovered');
      return;
    }

    print('✅ Starting async push of event ${event.id} to ${_knownNodes.length} nodes...');
    for (final node in _knownNodes) {
      print('📡 Will push to: ${node.name} (${node.ipAddress}:${node.port})');
    }

    // Create futures for all nodes with short timeout
    final futures = _knownNodes.map((node) =>
      _pushToSingleNodeAsync(node, event, records)
    ).toList();

    // Fire and forget - don't await, just handle completion
    Future.wait(futures, eagerError: false).then((results) {
      int successCount = results.where((success) => success).length;
      print('Async push completed: $successCount/${_knownNodes.length} nodes successful for event ${event.id}');
    }).catchError((error) {
      print('Error in async push: $error');
    });
  }

  /// Push to a single node asynchronously with short timeout
  Future<bool> _pushToSingleNodeAsync(Node node, Event event, List<Record> records) async {
    try {
      final url = '${node.baseUrl}/push';

      final response = await http
          .post(
            Uri.parse(url),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'events': [event.toJson()],
              'records': records.map((r) => r.toJson()).toList(),
              'sender_node_id': _nodeId,
            }),
          )
          .timeout(const Duration(seconds: 2)); // Short timeout for async push

      if (response.statusCode == 200) {
        print('Async push successful to ${node.name}');
        return true;
      } else {
        print('Async push failed to ${node.name}: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      print('Async push error to ${node.name}: $e');
      return false;
    }
  }

  /// Add a node manually (for testing or local network discovery)
  void addNode(Node node) {
    if (!_knownNodes.any((n) => n.id == node.id)) {
      _knownNodes.add(node);
    }
  }

  /// Remove a node
  void removeNode(String nodeId) {
    _knownNodes.removeWhere((node) => node.id == nodeId);
  }

  /// Get list of known nodes
  List<Node> get knownNodes => List.unmodifiable(_knownNodes);

  /// Print current sync status for debugging
  void printSyncStatus() {
    print('🔍 Sync Manager Status:');
    print('   - Initialized: $_isInitialized');
    print('   - Node ID: $_nodeId');
    print('   - Known nodes: ${_knownNodes.length}');
    for (final node in _knownNodes) {
      print('     * ${node.name} (${node.ipAddress}:${node.port}) - Online: ${node.isOnline}');
    }
    print('   - Tailscale configured: ${_tailscaleClient != null}');
  }

  /// Update the list of known nodes (called from TailscaleProvider)
  void updateKnownNodes(List<Node> nodes) {
    // Filter out this node based on current device ID
    _knownNodes = nodes.where((node) => node.id != _nodeId).toList();
    print('🔄 Updated known nodes: ${_knownNodes.length} nodes available');
    for (final node in _knownNodes) {
      print('   ✅ ${node.name} (${node.ipAddress}:${node.port}) - Online: ${node.isOnline}');
    }
  }

  /// Pull from a single node with timeout, returns null on failure
  Future<_PullResult?> _pullFromSingleNodeWithTimeout(Node node) async {
    try {
      return await _pullFromSingleNode(node).timeout(
        const Duration(seconds: 4),
        onTimeout: () => throw TimeoutException('Pull timeout', const Duration(seconds: 4)),
      );
    } catch (e) {
      print('Failed to pull from ${node.name}: $e');
      return null;
    }
  }

  /// Simple pull from a single node, returns structured result
  Future<_PullResult> _pullFromSingleNode(Node node) async {
    final url = '${node.baseUrl}/pull';

    final response = await http.get(
      Uri.parse(url),
      headers: {'Content-Type': 'application/json'},
    ).timeout(const Duration(seconds: 10));

    if (response.statusCode != 200) {
      throw Exception('Failed to pull from ${node.name}: ${response.statusCode}');
    }

    final data = jsonDecode(response.body) as Map<String, dynamic>;
    final eventsData = data['events'] as List<dynamic>? ?? [];
    final recordsData = data['records'] as List<dynamic>? ?? [];

    if (eventsData.isEmpty && recordsData.isEmpty) {
      print('No data received from ${node.name}');
      return _PullResult(events: [], records: []);
    }

    // Convert to objects
    final events = eventsData
        .map((eventData) => Event.fromJson(eventData as Map<String, dynamic>))
        .toList();

    final records = recordsData
        .map((recordData) => Record.fromJson(recordData as Map<String, dynamic>))
        .toList();

    return _PullResult(events: events, records: records);
  }

  /// Insert pulled data in a single transaction
  Future<void> _insertPulledDataInTransaction(List<Event> events, List<Record> records) async {
    int insertedEvents = 0;
    int insertedRecords = 0;

    await _databaseManager.database.transaction((txn) async {
      for (final event in events) {
        final result = await txn.insert(
          'events',
          event.toMap(),
          conflictAlgorithm: ConflictAlgorithm.ignore,
        );
        if (result != 0) insertedEvents++;
      }

      for (final record in records) {
        final result = await txn.insert(
          'records',
          record.toMap(),
          conflictAlgorithm: ConflictAlgorithm.ignore,
        );
        if (result != 0) insertedRecords++;
      }
    });

    print('Inserted in transaction: ${insertedEvents}/${events.length} events, ${insertedRecords}/${records.length} records');
  }

  /// Check if a node is reachable
  Future<bool> isNodeReachable(Node node) async {
    try {
      final response = await http.get(
        Uri.parse('${node.baseUrl}/health'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 5));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
