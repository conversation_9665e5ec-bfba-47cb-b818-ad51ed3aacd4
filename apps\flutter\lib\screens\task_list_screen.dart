import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:core/core.dart';

import '../providers/task_provider.dart';
import '../providers/tailscale_provider.dart';
import '../widgets/task_item.dart';
import '../widgets/add_task_dialog.dart';

/// Main screen showing the list of tasks
class TaskListScreen extends StatefulWidget {
  const TaskListScreen({super.key});

  @override
  State<TaskListScreen> createState() => _TaskListScreenState();
}

class _TaskListScreenState extends State<TaskListScreen> {
  // State for device events bottom drawer
  List<Event>? _deviceEvents;
  String? _deviceEventsError;
  bool _isLoadingDeviceEvents = false;
  String? _selectedDeviceName;

  /// Fetch events from a specific device
  Future<void> _fetchDeviceEvents(Node device) async {
    setState(() {
      _isLoadingDeviceEvents = true;
      _deviceEventsError = null;
      _deviceEvents = null;
      _selectedDeviceName = device.name;
    });

    try {
      final url = '${device.baseUrl}/events';
      final parsedUrl = Uri.parse(url);
      final response = await http
          .get(parsedUrl, headers: {'Content-Type': 'application/json'})
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body) as Map<String, dynamic>;
        final eventsData = data['events'] as List<dynamic>;

        final events = eventsData
            .map(
              (eventData) => Event.fromJson(eventData as Map<String, dynamic>),
            )
            .toList();

        setState(() {
          _deviceEvents = events;
          _isLoadingDeviceEvents = false;
        });
      } else {
        setState(() {
          _deviceEventsError = 'Error ${response.statusCode}: ${response.body}';
          _isLoadingDeviceEvents = false;
        });
      }
    } catch (e) {
      setState(() {
        _deviceEventsError = 'Failed to connect to device: $e';
        _isLoadingDeviceEvents = false;
      });
    }

    // Show the bottom drawer
    _showDeviceEventsDrawer();
  }

  /// Show the bottom drawer with device events
  void _showDeviceEventsDrawer() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => _buildDeviceEventsDrawer(),
    ).then((_) {
      // Clear data when drawer is closed
      setState(() {
        _deviceEvents = null;
        _deviceEventsError = null;
        _isLoadingDeviceEvents = false;
        _selectedDeviceName = null;
      });
    });
  }

  /// Build the device events drawer content
  Widget _buildDeviceEventsDrawer() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Handle bar
          Center(
            child: Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Title
          Row(
            children: [
              const Icon(Icons.event_note),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Events from ${_selectedDeviceName ?? 'Device'}',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Content
          Expanded(child: _buildEventsContent()),
        ],
      ),
    );
  }

  /// Build the events content based on current state
  Widget _buildEventsContent() {
    if (_isLoadingDeviceEvents) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading events...'),
          ],
        ),
      );
    }

    if (_deviceEventsError != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 48, color: Colors.red),
            const SizedBox(height: 16),
            Text(
              'Error loading events',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            Text(
              _deviceEventsError!,
              textAlign: TextAlign.center,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.red),
            ),
          ],
        ),
      );
    }

    if (_deviceEvents == null || _deviceEvents!.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.event_busy, size: 48, color: Colors.grey),
            SizedBox(height: 16),
            Text('No events found'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _deviceEvents!.length,
      itemBuilder: (context, index) {
        final event = _deviceEvents![index];
        return _buildEventCard(event);
      },
    );
  }

  /// Build a card for displaying an event
  Widget _buildEventCard(Event event) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Event header
            Row(
              children: [
                Icon(
                  _getEventIcon(event.eventType),
                  size: 16,
                  color: _getEventColor(event.eventType),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    event.eventType,
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: _getEventColor(event.eventType),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Text(
                  _formatTimestamp(event.timestamp),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Event details
            Row(
              children: [
                Text(
                  'Entity: ',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  '${event.entityType}/${event.entityId}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
            const SizedBox(height: 4),
            Row(
              children: [
                Text(
                  'Origin: ',
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
                ),
                Text(
                  event.origin,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),

            // Payload if not empty
            if (event.payload.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(
                'Payload:',
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _formatPayload(event.payload),
                  style: Theme.of(
                    context,
                  ).textTheme.bodySmall?.copyWith(fontFamily: 'monospace'),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Get icon for event type
  IconData _getEventIcon(String eventType) {
    switch (eventType) {
      case 'task_created':
        return Icons.add_task;
      case 'task_completed':
        return Icons.check_circle;
      case 'task_deleted':
        return Icons.delete;
      default:
        return Icons.event;
    }
  }

  /// Get color for event type
  Color _getEventColor(String eventType) {
    switch (eventType) {
      case 'task_created':
        return Colors.green;
      case 'task_completed':
        return Colors.blue;
      case 'task_deleted':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  /// Format timestamp for display
  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }

  /// Format payload for display
  String _formatPayload(Map<String, dynamic> payload) {
    try {
      const encoder = JsonEncoder.withIndent('  ');
      return encoder.convert(payload);
    } catch (e) {
      return payload.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Drift Tasks'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          Consumer2<TaskProvider, TailscaleProvider>(
            builder: (context, taskProvider, tailscaleProvider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Node info
                  Tooltip(
                    message: 'Node ID: ${taskProvider.nodeId}',
                    child: Icon(
                      Icons.device_hub,
                      color: tailscaleProvider.devices.isNotEmpty
                          ? Colors.green
                          : Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '${tailscaleProvider.devices.length}',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  // Debug sync status button (temporary)
                  IconButton(
                    onPressed: () {
                      taskProvider.printSyncStatus();
                    },
                    icon: const Icon(Icons.bug_report),
                    tooltip: 'Debug sync status',
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 8),
                  // Wipe data button
                  IconButton(
                    onPressed: tailscaleProvider.isLoading
                        ? null
                        : () {
                            _showWipeDataDialog(context, tailscaleProvider);
                          },
                    icon: const Icon(Icons.delete_forever),
                    tooltip: 'Wipe all data',
                    color: Colors.red,
                  ),
                  const SizedBox(width: 8),
                  // Refresh button
                  IconButton(
                    onPressed: taskProvider.isLoading
                        ? null
                        : () {
                            taskProvider.refresh();
                          },
                    icon: taskProvider.isLoading
                        ? const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Icon(Icons.refresh),
                    tooltip: 'Sync with other nodes',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer2<TaskProvider, TailscaleProvider>(
        builder: (context, taskProvider, tailscaleProvider, child) {
          return RefreshIndicator(
            onRefresh: () async {
              await taskProvider.refresh();
              await tailscaleProvider.refreshDevices();
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Tailscale Configuration Section
                  _buildTailscaleSection(context, tailscaleProvider),
                  const SizedBox(height: 24),

                  // Tasks Section
                  _buildTasksSection(context, taskProvider),
                  const SizedBox(height: 24),

                  // Devices Section
                  if (tailscaleProvider.isConfigured)
                    _buildDevicesSection(context, tailscaleProvider),
                ],
              ),
            ),
          );
        },
      ),
      floatingActionButton: Consumer<TaskProvider>(
        builder: (context, provider, child) {
          return FloatingActionButton(
            onPressed: provider.isLoading
                ? null
                : () {
                    _showAddTaskDialog(context);
                  },
            tooltip: 'Add Task',
            child: const Icon(Icons.add),
          );
        },
      ),
    );
  }

  void _showAddTaskDialog(BuildContext context) {
    showDialog(context: context, builder: (context) => const AddTaskDialog());
  }

  /// Show device selection modal after token input
  Future<void> _showDeviceSelectionModal(
    BuildContext context,
    TailscaleProvider provider,
    String token,
  ) async {
    // First, load devices with the token
    final devices = await provider.updateToken(token);

    if (devices.isEmpty) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
              'No devices found or failed to connect to Tailscale API',
            ),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: const Text('Select Your Device'),
          content: SizedBox(
            width: double.maxFinite,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  'Please select which device you are currently using:',
                  style: TextStyle(fontSize: 14),
                ),
                const SizedBox(height: 16),
                Flexible(
                  child: ListView.builder(
                    shrinkWrap: true,
                    itemCount: devices.length,
                    itemBuilder: (context, index) {
                      final device = devices[index];
                      return ListTile(
                        leading: Icon(
                          device.isOnline
                              ? Icons.computer
                              : Icons.computer_outlined,
                          color: device.isOnline ? Colors.green : Colors.grey,
                        ),
                        title: Text(device.name),
                        subtitle: Text('${device.ipAddress}:${device.port}'),
                        trailing: device.isOnline
                            ? const Icon(
                                Icons.circle,
                                color: Colors.green,
                                size: 12,
                              )
                            : const Icon(
                                Icons.circle,
                                color: Colors.grey,
                                size: 12,
                              ),
                        onTap: () async {
                          await provider.selectCurrentDevice(device.id);
                          if (context.mounted) {
                            Navigator.of(context).pop();
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(
                                  'Device "${device.name}" selected successfully',
                                ),
                                backgroundColor: Colors.green,
                              ),
                            );
                          }
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        ),
      );
    }
  }

  /// Show wipe data confirmation dialog
  void _showWipeDataDialog(
    BuildContext context,
    TailscaleProvider tailscaleProvider,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.warning, color: Colors.red),
            SizedBox(width: 8),
            Text('Wipe All Data'),
          ],
        ),
        content: const Text(
          'This will permanently delete the database file and recreate it with the latest schema.\n\n'
          'All data will be lost including:\n\n'
          '• All tasks and events\n'
          '• Tailscale configuration\n'
          '• Device settings\n'
          '• Logs and history\n\n'
          'The database will be recreated fresh with the latest schema version.\n\n'
          'This action cannot be undone. Are you sure?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await tailscaleProvider.wipeAllData();

              // Refresh task provider to reflect the clean state
              if (context.mounted) {
                final taskProvider = Provider.of<TaskProvider>(
                  context,
                  listen: false,
                );
                await taskProvider.refresh();

                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Database file deleted and recreated with latest schema',
                      ),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Wipe Data'),
          ),
        ],
      ),
    );
  }

  Widget _buildTailscaleSection(
    BuildContext context,
    TailscaleProvider provider,
  ) {
    final tokenController = TextEditingController();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tailscale Configuration',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: tokenController,
              decoration: InputDecoration(
                labelText: 'Access Token',
                hintText: provider.tokenMasked.isEmpty
                    ? 'tskey-...'
                    : provider.tokenMasked,
                border: const OutlineInputBorder(),
              ),
              obscureText: true,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ElevatedButton(
                  onPressed: provider.isLoading
                      ? null
                      : () async {
                          if (tokenController.text.isNotEmpty) {
                            await _showDeviceSelectionModal(
                              context,
                              provider,
                              tokenController.text,
                            );
                          }
                        },
                  child: provider.isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Configure'),
                ),
                const SizedBox(width: 8),
                if (provider.isConfigured)
                  OutlinedButton(
                    onPressed: provider.isLoading
                        ? null
                        : () async {
                            await provider.refreshDevices();
                          },
                    child: const Text('Refresh Devices'),
                  ),
              ],
            ),
            if (provider.error != null) ...[
              const SizedBox(height: 8),
              Text(
                provider.error!,
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTasksSection(BuildContext context, TaskProvider provider) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('Tasks', style: Theme.of(context).textTheme.titleMedium),
                IconButton(
                  onPressed: () => _showAddTaskDialog(context),
                  icon: const Icon(Icons.add),
                  tooltip: 'Add Task',
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (provider.error != null) ...[
              Text(
                'Error: ${provider.error}',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
              ),
              const SizedBox(height: 8),
            ],
            if (provider.tasks.isEmpty && !provider.isLoading)
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 16),
                child: Text('No tasks yet. Tap + to add your first task.'),
              )
            else
              ...provider.tasks.map(
                (task) => Padding(
                  padding: const EdgeInsets.only(bottom: 8),
                  child: TaskItem(
                    task: task,
                    onCompleted: () => provider.completeTask(task.id),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDevicesSection(
    BuildContext context,
    TailscaleProvider provider,
  ) {
    // Filter out the current device from the list
    final otherDevices = provider.devices
        .where((device) => device.id != provider.currentDeviceId)
        .toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Other Connected Devices',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 16),
            if (otherDevices.isEmpty)
              const Text('No other devices found')
            else
              ...otherDevices.map(
                (device) => ListTile(
                  leading: Icon(
                    device.isOnline ? Icons.computer : Icons.computer_outlined,
                    color: device.isOnline ? Colors.green : Colors.grey,
                  ),
                  title: Text(device.name),
                  subtitle: Text('${device.ipAddress}:${device.port}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      device.isOnline
                          ? const Icon(
                              Icons.circle,
                              color: Colors.green,
                              size: 12,
                            )
                          : const Icon(
                              Icons.circle,
                              color: Colors.grey,
                              size: 12,
                            ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward_ios, size: 16),
                    ],
                  ),
                  onTap: device.isOnline
                      ? () => _fetchDeviceEvents(device)
                      : null,
                ),
              ),
          ],
        ),
      ),
    );
  }
}
